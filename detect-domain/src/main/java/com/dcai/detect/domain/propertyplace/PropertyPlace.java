package com.dcai.detect.domain.propertyplace;

import com.ejuetc.commons.base.entity.BaseEntity;
import com.dcai.detect.dto.PropertyPlaceDTO;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.Where;

import java.io.Serializable;
import java.math.BigDecimal;

@Getter
@Entity
@Slf4j
@NoArgsConstructor
@Comment("物业场所")
@Table(name = "tb_property_place", 
       uniqueConstraints = @UniqueConstraint(name = "uk_property_place_code", columnNames = {"code"}),
       indexes = @Index(name = "idx_property_place_company_id", columnList = "property_company_id"))
@Where(clause = "logic_delete = 0")
public class PropertyPlace extends BaseEntity<PropertyPlace> implements Serializable {

    @Id
    @Comment("场所主键")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "name", columnDefinition = "varchar(127) COMMENT '场所名称'", nullable = false)
    private String name;

    @Column(name = "code", columnDefinition = "varchar(127) COMMENT '场所代码'", nullable = false, unique = true)
    private String code;

    @Enumerated(EnumType.STRING)
    @Column(name = "type", columnDefinition = "varchar(31) COMMENT '场所类型'", nullable = false)
    private PropertyPlaceDTO.PlaceType type;

    @Column(name = "property_company_id", columnDefinition = "bigint(20) COMMENT '所属物业公司ID'", nullable = false)
    private Long propertyCompanyId;

    @Column(name = "address", columnDefinition = "varchar(255) COMMENT '详细地址'")
    private String address;

    @Column(name = "building_area", columnDefinition = "decimal(10,2) COMMENT '建筑面积(平方米)'")
    private BigDecimal buildingArea;

    @Column(name = "usable_area", columnDefinition = "decimal(10,2) COMMENT '使用面积(平方米)'")
    private BigDecimal usableArea;

    @Column(name = "build_year", columnDefinition = "int COMMENT '建成年份'")
    private Integer buildYear;

    @Column(name = "floors", columnDefinition = "int COMMENT '楼层数'")
    private Integer floors;

    @Column(name = "contact_person", columnDefinition = "varchar(63) COMMENT '联系人'")
    private String contactPerson;

    @Column(name = "contact_phone", columnDefinition = "varchar(31) COMMENT '联系电话'")
    private String contactPhone;

    @Enumerated(EnumType.STRING)
    @Column(name = "status", columnDefinition = "varchar(31) COMMENT '场所状态'", nullable = false)
    private PropertyPlaceDTO.PlaceStatus status;

    @Column(name = "remark", columnDefinition = "text COMMENT '备注'")
    private String remark;

    public PropertyPlace(String name, String code, PropertyPlaceDTO.PlaceType type, Long propertyCompanyId,
                        String address, BigDecimal buildingArea, BigDecimal usableArea, Integer buildYear,
                        Integer floors, String contactPerson, String contactPhone, String remark) {
        this.name = name;
        this.code = code;
        this.type = type;
        this.propertyCompanyId = propertyCompanyId;
        this.address = address;
        this.buildingArea = buildingArea;
        this.usableArea = usableArea;
        this.buildYear = buildYear;
        this.floors = floors;
        this.contactPerson = contactPerson;
        this.contactPhone = contactPhone;
        this.remark = remark;
        this.status = PropertyPlaceDTO.PlaceStatus.ACTIVE;
    }

    public void updateInfo(String name, String address, BigDecimal buildingArea, BigDecimal usableArea,
                          Integer buildYear, Integer floors, String contactPerson, String contactPhone, String remark) {
        this.name = name;
        this.address = address;
        this.buildingArea = buildingArea;
        this.usableArea = usableArea;
        this.buildYear = buildYear;
        this.floors = floors;
        this.contactPerson = contactPerson;
        this.contactPhone = contactPhone;
        this.remark = remark;
    }

    public void activate() {
        this.status = PropertyPlaceDTO.PlaceStatus.ACTIVE;
    }

    public void deactivate() {
        this.status = PropertyPlaceDTO.PlaceStatus.INACTIVE;
    }

    public boolean isActive() {
        return PropertyPlaceDTO.PlaceStatus.ACTIVE.equals(this.status);
    }
}
